package ir.rahavardit.ariel.ui.edittransaction

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.BankItem
import ir.rahavardit.ariel.data.model.NewTransactionRequest
import ir.rahavardit.ariel.data.model.TagItem
import ir.rahavardit.ariel.data.model.TransactionCategoryItem
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.data.model.TransactionResponse
import ir.rahavardit.ariel.data.repository.TransactionRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the EditTransactionFragment.
 */
class EditTransactionViewModel : ViewModel() {

    private val transactionRepository = TransactionRepository()

    // Loading state
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // Error state
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    // Transaction modes
    private val _transactionModes = MutableLiveData<List<TransactionMode>>()
    val transactionModes: LiveData<List<TransactionMode>> = _transactionModes

    // Banks
    private val _banks = MutableLiveData<List<BankItem>>()
    val banks: LiveData<List<BankItem>> = _banks

    // Categories
    private val _categories = MutableLiveData<List<TransactionCategoryItem>>()
    val categories: LiveData<List<TransactionCategoryItem>> = _categories

    // Tags
    private val _tags = MutableLiveData<List<TagItem>>()
    val tags: LiveData<List<TagItem>> = _tags

    // Transaction update result
    private val _transactionUpdateResult = MutableLiveData<TransactionUpdateResult>()
    val transactionUpdateResult: LiveData<TransactionUpdateResult> = _transactionUpdateResult

    /**
     * Sealed class representing the result of transaction update operation.
     */
    sealed class TransactionUpdateResult {
        data class Success(val transaction: TransactionResponse) : TransactionUpdateResult()
        data class Error(val errorMessage: String) : TransactionUpdateResult()
    }

    /**
     * Data class for input validation results.
     */
    data class ValidationResult(
        val isDateValid: Boolean,
        val isAmountValid: Boolean,
        val isModeValid: Boolean,
        val isCategoryValid: Boolean
    )

    /**
     * Loads transaction modes from the API.
     *
     * @param token The authentication token.
     */
    fun loadTransactionModes(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getTransactionModes(token)
                result.fold(
                    onSuccess = { modes ->
                        _transactionModes.value = modes
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to load transaction modes"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to load transaction modes"
            }
        }
    }

    /**
     * Loads banks from the API.
     *
     * @param token The authentication token.
     */
    fun loadBanks(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getBanks(token)
                result.fold(
                    onSuccess = { bankResponse ->
                        _banks.value = bankResponse.results
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to load banks"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to load banks"
            }
        }
    }

    /**
     * Loads categories from the API.
     *
     * @param token The authentication token.
     */
    fun loadCategories(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getTransactionCategories(token)
                result.fold(
                    onSuccess = { categoryResponse ->
                        _categories.value = categoryResponse.results
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to load categories"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to load categories"
            }
        }
    }

    /**
     * Loads tags from the API.
     *
     * @param token The authentication token.
     */
    fun loadTags(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getTags(token)
                result.fold(
                    onSuccess = { tagResponse ->
                        _tags.value = tagResponse.results
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to load tags"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to load tags"
            }
        }
    }

    /**
     * Validates the input fields.
     *
     * @param date The transaction date.
     * @param amount The transaction amount.
     * @param mode The transaction mode.
     * @param categoryId The category ID.
     * @return A ValidationResult indicating which fields are valid.
     */
    fun validateInputs(
        date: String,
        amount: String,
        mode: String?,
        categoryId: Int?
    ): ValidationResult {
        return ValidationResult(
            isDateValid = date.isNotBlank(),
            isAmountValid = amount.isNotBlank() && amount.toIntOrNull() != null && amount.toInt() > 0,
            isModeValid = !mode.isNullOrBlank(),
            isCategoryValid = categoryId != null
        )
    }

    /**
     * Updates a transaction.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the transaction to update.
     * @param date The transaction date in YYYY/MM/DD format.
     * @param title The transaction title (optional).
     * @param amount The transaction amount.
     * @param mode The transaction mode value.
     * @param bankId The bank ID (optional).
     * @param categoryId The category ID.
     * @param tagIds The list of tag IDs.
     */
    fun updateTransaction(
        token: String,
        shortUuid: String,
        date: String,
        title: String?,
        amount: Int,
        mode: String,
        bankId: Int?,
        categoryId: Int,
        tagIds: List<Int>
    ) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val request = NewTransactionRequest(
                    date = date,
                    title = if (title.isNullOrBlank()) null else title,
                    amount = amount,
                    mode = mode,
                    bank = bankId?.toString(),
                    category = categoryId.toString(),
                    tags = tagIds
                )

                val result = transactionRepository.updateTransaction(token, shortUuid, request)

                result.fold(
                    onSuccess = { transaction ->
                        _transactionUpdateResult.value = TransactionUpdateResult.Success(transaction)
                    },
                    onFailure = { exception ->
                        _transactionUpdateResult.value = TransactionUpdateResult.Error(
                            exception.message ?: "Failed to update transaction"
                        )
                    }
                )
            } catch (e: Exception) {
                _transactionUpdateResult.value = TransactionUpdateResult.Error(
                    e.message ?: "Failed to update transaction"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clears the error message.
     */
    fun clearError() {
        _error.value = null
    }
}
